import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/models/expense.dart';
import 'package:inventory_management_app/screens/expenses/expense_details_screen.dart';

class ExpensesScreen extends StatelessWidget {
  const ExpensesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ExpenseProvider expenseProvider =
        Provider.of<ExpenseProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Expenses'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (BuildContext context) =>
                      const ExpenseDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: expenseProvider.expenses.length,
        itemBuilder: (BuildContext context, int index) {
          final Expense expense = expenseProvider.expenses[index];
          return Card(
            child: ListTile(
              title: Text(expense.category ?? ''),
              subtitle: Text(expense.description ?? ''),
              trailing:
                  Text('\$${expense.amount?.toStringAsFixed(2) ?? '0.00'}'),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        ExpenseDetailsScreen(expense: expense),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
