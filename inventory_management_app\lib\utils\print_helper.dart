import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../utils/formatters.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة مساعدة للطباعة
class PrintHelper {
  /// طباعة فاتورة البيع
  static Future<Uint8List?> generateSaleInvoice(
      Sale sale, List<Map<String, dynamic>> items) async {
    try {
      final String html = _generateSaleInvoiceHTML(sale, items);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// طباعة فاتورة الشراء
  static Future<Uint8List?> generatePurchaseInvoice(
      Purchase purchase, List<Map<String, dynamic>> items) async {
    try {
      final String html = _generatePurchaseInvoiceHTML(purchase, items);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// طباعة قائمة المنتجات
  static Future<Uint8List?> generateProductsList(List<Product> products) async {
    try {
      final String html = _generateProductsListHTML(products);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// طباعة قائمة العملاء
  static Future<Uint8List?> generateCustomersList(
      List<Customer> customers) async {
    try {
      final String html = _generateCustomersListHTML(customers);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// طباعة تقرير المبيعات
  static Future<Uint8List?> generateSalesReport(
    List<Sale> sales,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final String html = _generateSalesReportHTML(sales, startDate, endDate);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// طباعة باركود المنتج
  static Future<Uint8List?> generateProductBarcode(Product product) async {
    try {
      final String html = _generateProductBarcodeHTML(product);
      // TODO: تحويل HTML إلى PDF
      return null;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء HTML لفاتورة البيع
  static String _generateSaleInvoiceHTML(
      Sale sale, List<Map<String, dynamic>> items) {
    final double totalAmount = sale.total ?? 0;
    final double itemsTotal = items.fold<double>(
        0,
        (double sum, Map<String, dynamic> item) =>
            sum + ((item['quantity'] ?? 0) * (item['price'] ?? 0)));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة بيع</title>
    <style>
        ${_getCommonCSS()}
        .invoice-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .total-section {
            text-align: left;
            margin-top: 20px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .grand-total {
            font-size: 18px;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>فاتورة بيع</h2>
        </div>
        
        <div class="invoice-info">
            <div>
                <p><strong>رقم الفاتورة:</strong> ${sale.invoiceNumber ?? sale.id}</p>
                <p><strong>التاريخ:</strong> ${sale.date != null ? DateHelper.formatDate(sale.date!) : ''}</p>
                <p><strong>طريقة الدفع:</strong> ${Formatters.formatPaymentMethod(sale.paymentMethod ?? '')}</p>
            </div>
            <div>
                <p><strong>العميل:</strong> ${sale.customerName ?? 'عميل نقدي'}</p>
                <p><strong>الحالة:</strong> ${_formatStatus(sale.status ?? '')}</p>
            </div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((Map<String, dynamic> item) => '''
                <tr>
                    <td>${item['productName'] ?? ''}</td>
                    <td>${item['quantity'] ?? 0}</td>
                    <td>${Formatters.formatCurrency(item['price'] ?? 0)}</td>
                    <td>${Formatters.formatCurrency((item['quantity'] ?? 0) * (item['price'] ?? 0))}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="total-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>${Formatters.formatCurrency(itemsTotal)}</span>
            </div>
            <div class="total-row grand-total">
                <span>المجموع الإجمالي:</span>
                <span>${Formatters.formatCurrency(totalAmount)}</span>
            </div>
        </div>
        
        ${sale.notes?.isNotEmpty == true ? '''
        <div class="notes">
            <h3>ملاحظات:</h3>
            <p>${sale.notes}</p>
        </div>
        ''' : ''}
        
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لفاتورة الشراء
  static String _generatePurchaseInvoiceHTML(
      Purchase purchase, List<Map<String, dynamic>> items) {
    final double totalAmount = purchase.total ?? 0;
    final double itemsTotal = items.fold<double>(
        0,
        (double sum, Map<String, dynamic> item) =>
            sum + ((item['quantity'] ?? 0) * (item['price'] ?? 0)));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة شراء</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>فاتورة شراء</h2>
        </div>
        
        <div class="invoice-info">
            <div>
                <p><strong>رقم الفاتورة:</strong> ${purchase.invoiceNumber ?? purchase.id}</p>
                <p><strong>التاريخ:</strong> ${purchase.date != null ? DateHelper.formatDate(purchase.date!) : ''}</p>
            </div>
            <div>
                <p><strong>المورد:</strong> ${purchase.supplierName ?? ''}</p>
                <p><strong>الحالة:</strong> ${_formatStatus(purchase.status ?? '')}</p>
            </div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((Map<String, dynamic> item) => '''
                <tr>
                    <td>${item['productName'] ?? ''}</td>
                    <td>${item['quantity'] ?? 0}</td>
                    <td>${Formatters.formatCurrency(item['price'] ?? 0)}</td>
                    <td>${Formatters.formatCurrency((item['quantity'] ?? 0) * (item['price'] ?? 0))}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="total-section">
            <div class="total-row grand-total">
                <span>المجموع الإجمالي:</span>
                <span>${Formatters.formatCurrency(totalAmount)}</span>
            </div>
        </div>
        
        <div class="footer">
            <p>تم إنشاء هذه الفاتورة بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لقائمة المنتجات
  static String _generateProductsListHTML(List<Product> products) {
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المنتجات</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>قائمة المنتجات</h2>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الفئة</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>سعر الشراء</th>
                    <th>سعر البيع</th>
                    <th>الحد الأدنى</th>
                </tr>
            </thead>
            <tbody>
                ${products.map((Product product) => '''
                <tr>
                    <td>${product.name ?? ''}</td>
                    <td>${product.category ?? ''}</td>
                    <td>${product.quantity ?? 0}</td>
                    <td>${product.unit ?? ''}</td>
                    <td>${Formatters.formatCurrency(product.purchasePrice ?? 0)}</td>
                    <td>${Formatters.formatCurrency(product.salePrice ?? 0)}</td>
                    <td>${product.minLevel ?? 0}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>إجمالي المنتجات: ${products.length}</p>
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لقائمة العملاء
  static String _generateCustomersListHTML(List<Customer> customers) {
    final double totalBalance = customers.fold<double>(
        0, (double sum, Customer customer) => sum + (customer.balance ?? 0));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة العملاء</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>قائمة العملاء</h2>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>العنوان</th>
                    <th>الرصيد</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map((Customer customer) => '''
                <tr>
                    <td>${customer.name ?? ''}</td>
                    <td>${customer.phone ?? ''}</td>
                    <td>${customer.email ?? ''}</td>
                    <td>${customer.address ?? ''}</td>
                    <td>${Formatters.formatCurrency(customer.balance ?? 0)}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>إجمالي العملاء: ${customers.length}</p>
            <p>إجمالي الأرصدة: ${Formatters.formatCurrency(totalBalance)}</p>
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لتقرير المبيعات
  static String _generateSalesReportHTML(
      List<Sale> sales, DateTime startDate, DateTime endDate) {
    final double totalAmount = sales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));
    final int completedSales =
        sales.where((Sale sale) => sale.status == 'completed').length;

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>تقرير المبيعات</h2>
            <p>من ${DateHelper.formatDate(startDate)} إلى ${DateHelper.formatDate(endDate)}</p>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <div class="summary">
            <h3>ملخص التقرير</h3>
            <p>إجمالي المبيعات: ${sales.length}</p>
            <p>المبيعات المكتملة: $completedSales</p>
            <p>إجمالي المبلغ: ${Formatters.formatCurrency(totalAmount)}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${sales.map((Sale sale) => '''
                <tr>
                    <td>${sale.invoiceNumber ?? sale.id}</td>
                    <td>${sale.date != null ? DateHelper.formatDate(sale.date!) : ''}</td>
                    <td>${sale.customerName ?? 'عميل نقدي'}</td>
                    <td>${Formatters.formatCurrency(sale.total ?? 0)}</td>
                    <td>${Formatters.formatPaymentMethod(sale.paymentMethod ?? '')}</td>
                    <td>${_formatStatus(sale.status ?? '')}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لباركود المنتج
  static String _generateProductBarcodeHTML(Product product) {
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>باركود المنتج</title>
    <style>
        ${_getCommonCSS()}
        .barcode-container {
            text-align: center;
            margin: 50px 0;
        }
        .barcode {
            font-family: 'Libre Barcode 128', monospace;
            font-size: 48px;
            margin: 20px 0;
        }
        .product-info {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="barcode-container">
            <div class="product-info">
                <h2>${product.name ?? ''}</h2>
                <p>الفئة: ${product.category ?? ''}</p>
                <p>السعر: ${Formatters.formatCurrency(product.salePrice ?? 0)}</p>
            </div>
            
            <div class="barcode">
                ${product.barcode ?? ''}
            </div>
            
            <p>${product.barcode ?? ''}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// الحصول على CSS المشترك
  static String _getCommonCSS() {
    return '''
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1, h2, h3 {
            margin-bottom: 10px;
        }
        
        p {
            margin-bottom: 5px;
        }
        
        .summary {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .notes {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 10px;
            }
        }
    ''';
  }

  /// تنسيق الحالة
  static String _formatStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      case 'draft':
        return 'مسودة';
      default:
        return status;
    }
  }

  /// طباعة HTML مباشرة (للويب)
  static void printHTML(String html) {
    // TODO: تنفيذ الطباعة للويب
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfBytes, String fileName) async {
    // TODO: تنفيذ مشاركة PDF
  }

  /// حفظ PDF
  static Future<String?> savePDF(Uint8List pdfBytes, String fileName) async {
    // TODO: تنفيذ حفظ PDF
    return null;
  }
}
