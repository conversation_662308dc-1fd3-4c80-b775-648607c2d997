import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/sale_provider.dart';
import '../../models/sale.dart';
import '../../models/sale_item.dart';
import '../../models/customer.dart';
import '../../models/product.dart';
import '../dialogs/select_customer_dialog.dart';
import '../dialogs/add_product_to_invoice_dialog.dart';
import '../dialogs/confirmation_dialog.dart';
import 'sale_invoice_details_screen.dart';

class CreateSaleInvoiceScreen extends StatefulWidget {
  final Sale? existingSale; // للتعديل

  const CreateSaleInvoiceScreen({
    super.key,
    this.existingSale,
  });

  @override
  State<CreateSaleInvoiceScreen> createState() =>
      _CreateSaleInvoiceScreenState();
}

class _CreateSaleInvoiceScreenState extends State<CreateSaleInvoiceScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _invoiceNumberController =
      TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _customerController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // حقول جديدة لنظام الجملة والتجزئة
  final TextEditingController _retailTotalAmountController =
      TextEditingController();
  final TextEditingController _remainingRetailAmountController =
      TextEditingController();
  final TextEditingController _notesForRetailItemsController =
      TextEditingController();

  Customer? _selectedCustomer;
  DateTime _selectedDate = DateTime.now();
  final List<SaleItem> _saleItems = <SaleItem>[];
  bool _isLoading = false;

  // متغيرات لنظام الجملة والتجزئة
  String _selectedSaleType = 'wholesale'; // 'wholesale' أو 'retail'

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingSale != null) {
      // تحميل بيانات الفاتورة الموجودة للتعديل
      final Sale sale = widget.existingSale!;
      _invoiceNumberController.text = sale.id?.toString() ?? '';
      _selectedDate =
          DateTime.parse(sale.date ?? DateTime.now().toIso8601String());
      _dateController.text = _formatDate(_selectedDate);
      _notesController.text = sale.notes ?? '';

      // تحميل العميل
      if (sale.customerId != null) {
        final CustomerProvider customerProvider =
            context.read<CustomerProvider>();
        _selectedCustomer = customerProvider.customers
            .where((Customer c) => c.id == sale.customerId)
            .firstOrNull;
        _customerController.text = _selectedCustomer?.name ?? '';
      }

      // تحميل عناصر الفاتورة
      // TODO: تحميل SaleItems من قاعدة البيانات
    } else {
      // إنشاء فاتورة جديدة
      _invoiceNumberController.text = _generateInvoiceNumber();
      _dateController.text = _formatDate(_selectedDate);
    }
  }

  String _generateInvoiceNumber() {
    final DateTime now = DateTime.now();
    return 'INV-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  double get _totalAmount {
    return _totalWholesaleAmount + _totalRetailAmount;
  }

  double get _totalWholesaleAmount {
    return _saleItems
        .where((SaleItem item) => item.itemType == 'wholesale')
        .fold(
            0.0,
            (double sum, SaleItem item) =>
                sum + ((item.quantity ?? 0) * (item.price ?? 0)));
  }

  double get _totalRetailAmount {
    return _saleItems
        .where((SaleItem item) => item.itemType == 'retail')
        .fold(0.0, (double sum, SaleItem item) => sum + (item.price ?? 0));
  }

  double get _remainingRetailAmount {
    final double retailAmountFromInput =
        double.tryParse(_remainingRetailAmountController.text) ?? 0;
    return retailAmountFromInput;
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(widget.existingSale != null
              ? 'تعديل فاتورة بيع'
              : 'فاتورة بيع جديدة'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSaleInvoice,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // رقم الفاتورة
                      TextFormField(
                        controller: _invoiceNumberController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الفاتورة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.receipt_long),
                        ),
                        readOnly: true,
                      ),

                      const SizedBox(height: 16),

                      // التاريخ
                      TextFormField(
                        controller: _dateController,
                        decoration: const InputDecoration(
                          labelText: 'التاريخ',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectDate,
                      ),

                      const SizedBox(height: 16),

                      // العميل
                      TextFormField(
                        controller: _customerController,
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.person),
                          suffixIcon: Icon(Icons.search),
                        ),
                        readOnly: true,
                        onTap: _selectCustomer,
                        validator: (String? value) {
                          if (_selectedCustomer == null) {
                            return 'يرجى اختيار العميل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 24),

                      // قسم نوع البيع
                      _buildSaleTypeSection(),

                      const SizedBox(height: 24),

                      // قسم منتجات الجملة
                      if (_selectedSaleType == 'wholesale')
                        _buildWholesaleSection(),

                      // قسم بنود التجزئة
                      if (_selectedSaleType == 'retail') _buildRetailSection(),

                      const SizedBox(height: 16),

                      // قائمة المنتجات
                      _buildProductsList(),

                      const SizedBox(height: 24),

                      // عرض الإجماليات
                      _buildTotalsSection(),

                      const SizedBox(height: 16),

                      // الملاحظات
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // شريط الأزرار السفلي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveSaleInvoice,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            widget.existingSale != null
                                ? 'حفظ التعديلات'
                                : 'حفظ الفاتورة',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    if (_saleItems.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Column(
          children: <Widget>[
            Icon(Icons.shopping_cart_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'لم يتم إضافة أي منتجات بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _saleItems.length,
      itemBuilder: (BuildContext context, int index) {
        final SaleItem item = _saleItems[index];
        return _buildProductItem(item, index);
      },
    );
  }

  Widget _buildProductItem(SaleItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    item.productName ?? 'منتج غير محدد',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'النوع: ${item.itemType == 'wholesale' ? 'جملة' : 'تجزئة'}',
                    style: TextStyle(
                      color: item.itemType == 'wholesale'
                          ? Colors.green
                          : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'الكمية: ${item.quantity?.toStringAsFixed(0) ?? '0'}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  Text(
                    'السعر: ${item.unitPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            Column(
              children: <Widget>[
                Text(
                  '${item.totalPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    IconButton(
                      onPressed: () => _editProductQuantity(index),
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      iconSize: 20,
                    ),
                    IconButton(
                      onPressed: () => _removeProduct(index),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      iconSize: 20,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _selectCustomer() async {
    final Customer? customer = await showDialog<Customer>(
      context: context,
      builder: (BuildContext context) => const SelectCustomerDialog(),
    );

    if (customer != null) {
      setState(() {
        _selectedCustomer = customer;
        _customerController.text = customer.name;
      });
    }
  }

  Future<void> _addProduct() async {
    // استخدام دالة إضافة منتجات الجملة
    await _addWholesaleProduct();
  }

  void _editProductQuantity(int index) {
    final SaleItem item = _saleItems[index];
    final TextEditingController quantityController = TextEditingController(
      text: item.quantity?.toString() ?? '1',
    );

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تعديل الكمية'),
        content: TextFormField(
          controller: quantityController,
          decoration: const InputDecoration(
            labelText: 'الكمية',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final double? newQuantity =
                  double.tryParse(quantityController.text);
              if (newQuantity != null && newQuantity > 0) {
                setState(() {
                  _saleItems[index] = _saleItems[index].copyWith(
                    quantity: newQuantity,
                    totalPrice: newQuantity * (item.unitPrice ?? 0),
                  );
                });
              }
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _removeProduct(int index) {
    setState(() {
      _saleItems.removeAt(index);
    });
  }

  Future<void> _saveSaleInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_saleItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة منتج واحد على الأقل')),
      );
      return;
    }

    // عرض تأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => ConfirmationDialog(
        title: widget.existingSale != null ? 'تأكيد التعديل' : 'تأكيد الحفظ',
        content: widget.existingSale != null
            ? 'هل أنت متأكد من تعديل هذه الفاتورة؟ قد يؤثر ذلك على المخزون والسجلات المالية.'
            : 'هل أنت متأكد من إتمام هذه الفاتورة؟',
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final SaleProvider saleProvider = context.read<SaleProvider>();

      final Sale sale = Sale(
        id: widget.existingSale?.id,
        customerId: _selectedCustomer!.id,
        date: _selectedDate.toIso8601String(),
        totalAmount: _totalAmount,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        status: 'completed',
        totalWholesaleAmount: _totalWholesaleAmount,
        totalRetailAmount: _totalRetailAmount,
        remainingRetailAmount: _remainingRetailAmount,
        notesForRetailItems: _notesForRetailItemsController.text.trim().isEmpty
            ? null
            : _notesForRetailItemsController.text.trim(),
      );

      Sale savedSale;
      if (widget.existingSale != null) {
        savedSale = await saleProvider.updateSale(sale, _saleItems);
      } else {
        savedSale = await saleProvider.addSale(sale, _saleItems);
      }

      if (mounted) {
        // الانتقال لشاشة التفاصيل
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) =>
                SaleInvoiceDetailsScreen(sale: savedSale),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildSaleTypeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Text(
            'نوع البيع',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('بيع بالجملة'),
                  subtitle: const Text('منتجات مفصلة من المخزن'),
                  value: 'wholesale',
                  groupValue: _selectedSaleType,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedSaleType = value!;
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('بيع بالتجزئة'),
                  subtitle: const Text('مبلغ إجمالي من البقالة'),
                  value: 'retail',
                  groupValue: _selectedSaleType,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedSaleType = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWholesaleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            const Text(
              'منتجات الجملة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _addWholesaleProduct,
              icon: const Icon(Icons.add),
              label: const Text('إضافة منتج'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildWholesaleProductsList(),
      ],
    );
  }

  Widget _buildRetailSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'بنود التجزئة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // مبلغ التجزئة الإجمالي
        TextFormField(
          controller: _retailTotalAmountController,
          decoration: const InputDecoration(
            labelText: 'مبلغ التجزئة الإجمالي',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.attach_money),
            suffixText: 'ر.س',
          ),
          keyboardType: TextInputType.number,
          onChanged: (String value) {
            setState(() {}); // لتحديث الإجماليات
          },
        ),

        const SizedBox(height: 16),

        // المبلغ المتبقي كدين
        TextFormField(
          controller: _remainingRetailAmountController,
          decoration: const InputDecoration(
            labelText: 'المبلغ المتبقي كدين',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.credit_card),
            suffixText: 'ر.س',
          ),
          keyboardType: TextInputType.number,
          onChanged: (String value) {
            setState(() {}); // لتحديث الإجماليات
          },
        ),

        const SizedBox(height: 16),

        // ملاحظات التجزئة
        TextFormField(
          controller: _notesForRetailItemsController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات على بنود التجزئة',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 2,
        ),

        const SizedBox(height: 16),

        // زر إضافة بند تجزئة
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _addRetailItem,
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('إضافة بند تجزئة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(height: 16),
        _buildRetailItemsList(),
      ],
    );
  }

  Widget _buildTotalsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: <Widget>[
          if (_totalWholesaleAmount > 0) ...<Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text('إجمالي الجملة:', style: TextStyle(fontSize: 16)),
                Text(
                  '${_totalWholesaleAmount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          if (_totalRetailAmount > 0) ...<Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text('إجمالي التجزئة:', style: TextStyle(fontSize: 16)),
                Text(
                  '${_totalRetailAmount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          if (_remainingRetailAmount > 0) ...<Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text('المبلغ المتبقي كدين:',
                    style: TextStyle(fontSize: 16)),
                Text(
                  '${_remainingRetailAmount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Text(
                'المبلغ الإجمالي:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                '${_totalAmount.toStringAsFixed(2)} ر.س',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWholesaleProductsList() {
    final List<SaleItem> wholesaleItems = _saleItems
        .where((SaleItem item) => item.itemType == 'wholesale')
        .toList();

    if (wholesaleItems.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Column(
          children: <Widget>[
            Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'لم يتم إضافة أي منتجات جملة بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: wholesaleItems.length,
      itemBuilder: (BuildContext context, int index) {
        final SaleItem item = wholesaleItems[index];
        final int originalIndex = _saleItems.indexOf(item);
        return _buildProductItem(item, originalIndex);
      },
    );
  }

  Widget _buildRetailItemsList() {
    final List<SaleItem> retailItems =
        _saleItems.where((SaleItem item) => item.itemType == 'retail').toList();

    if (retailItems.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Column(
          children: <Widget>[
            Icon(Icons.shopping_cart_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'لم يتم إضافة أي بنود تجزئة بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: retailItems.length,
      itemBuilder: (BuildContext context, int index) {
        final SaleItem item = retailItems[index];
        final int originalIndex = _saleItems.indexOf(item);
        return _buildRetailItem(item, originalIndex);
      },
    );
  }

  Widget _buildRetailItem(SaleItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.shopping_cart, color: Colors.orange),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    item.productName ?? 'بند تجزئة',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'نوع: تجزئة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            Column(
              children: <Widget>[
                Text(
                  '${item.price?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 8),
                IconButton(
                  onPressed: () => _removeProduct(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  iconSize: 20,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addWholesaleProduct() async {
    final SaleItem? newItem = await showDialog<SaleItem>(
      context: context,
      builder: (BuildContext context) => const AddProductToInvoiceDialog(),
    );

    if (newItem != null) {
      setState(() {
        _saleItems.add(newItem);
      });
    }
  }

  void _addRetailItem() {
    final double? retailAmount =
        double.tryParse(_retailTotalAmountController.text);

    if (retailAmount == null || retailAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال مبلغ التجزئة الإجمالي')),
      );
      return;
    }

    final SaleItem retailItem = SaleItem(
      productName:
          'بند تجزئة - ${_notesForRetailItemsController.text.isNotEmpty ? _notesForRetailItemsController.text : 'مبيعات البقالة'}',
      quantity: 1,
      price: retailAmount,
      itemType: 'retail',
    );

    setState(() {
      _saleItems.add(retailItem);
      // مسح الحقول بعد الإضافة
      _retailTotalAmountController.clear();
      _notesForRetailItemsController.clear();
    });
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _dateController.dispose();
    _customerController.dispose();
    _notesController.dispose();
    _retailTotalAmountController.dispose();
    _remainingRetailAmountController.dispose();
    _notesForRetailItemsController.dispose();
    super.dispose();
  }
}
