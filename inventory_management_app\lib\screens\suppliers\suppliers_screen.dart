import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/models/supplier.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_details_screen.dart';

class SuppliersScreen extends StatelessWidget {
  const SuppliersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final SupplierProvider supplierProvider =
        Provider.of<SupplierProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Suppliers'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (BuildContext context) =>
                      const SupplierDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: supplierProvider.suppliers.length,
        itemBuilder: (BuildContext context, int index) {
          final Supplier supplier = supplierProvider.suppliers[index];
          return Card(
            child: ListTile(
              title: Text(supplier.name),
              subtitle: Text(supplier.email ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              SupplierDetailsScreen(supplier: supplier),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      supplierProvider.deleteSupplier(supplier.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
