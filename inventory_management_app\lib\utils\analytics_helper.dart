import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../utils/settings_helper.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة مساعدة للتحليلات والإحصائيات
class AnalyticsHelper {
  static final Map<String, dynamic> _analyticsData = <String, dynamic>{};
  static bool _isEnabled = true;

  /// تهيئة التحليلات
  static Future<void> initialize() async {
    _isEnabled = SettingsHelper.getAnalytics();
    if (_isEnabled) {
      await _loadAnalyticsData();
    }
  }

  /// تسجيل حدث
  static Future<void> logEvent(String eventName,
      {Map<String, dynamic>? parameters}) async {
    if (!_isEnabled) return;

    try {
      final Map<String, Object> event = <String, Object>{
        'name': eventName,
        'timestamp': DateTime.now().toIso8601String(),
        'parameters': parameters ?? <String, dynamic>{},
      };

      _addEventToData(event);
      await _saveAnalyticsData();

      if (kDebugMode) {
        print('Analytics Event: $eventName - $parameters');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تسجيل الحدث: $e');
      }
    }
  }

  /// تسجيل عرض الشاشة
  static Future<void> logScreenView(String screenName) async {
    await logEvent('screen_view', parameters: <String, dynamic>{
      'screen_name': screenName,
    });
  }

  /// تسجيل إضافة منتج
  static Future<void> logProductAdded(Product product) async {
    await logEvent('product_added', parameters: <String, dynamic>{
      'product_id': product.id,
      'product_name': product.name,
      'category': product.category,
      'price': product.salePrice,
    });
  }

  /// تسجيل تعديل منتج
  static Future<void> logProductUpdated(Product product) async {
    await logEvent('product_updated', parameters: <String, dynamic>{
      'product_id': product.id,
      'product_name': product.name,
      'category': product.category,
    });
  }

  /// تسجيل حذف منتج
  static Future<void> logProductDeleted(int productId) async {
    await logEvent('product_deleted', parameters: <String, dynamic>{
      'product_id': productId,
    });
  }

  /// تسجيل إضافة عميل
  static Future<void> logCustomerAdded(Customer customer) async {
    await logEvent('customer_added', parameters: <String, dynamic>{
      'customer_id': customer.id,
      'customer_name': customer.name,
    });
  }

  /// تسجيل بيع جديد
  static Future<void> logSaleCreated(Sale sale) async {
    await logEvent('sale_created', parameters: <String, dynamic>{
      'sale_id': sale.id,
      'customer_id': sale.customerId,
      'total_amount': sale.total,
      'payment_method': sale.paymentMethod,
    });
  }

  /// تسجيل شراء جديد
  static Future<void> logPurchaseCreated(Purchase purchase) async {
    await logEvent('purchase_created', parameters: <String, dynamic>{
      'purchase_id': purchase.id,
      'supplier_id': purchase.supplierId,
      'total_amount': purchase.total,
    });
  }

  /// تسجيل البحث
  static Future<void> logSearch(String query, String category) async {
    await logEvent('search', parameters: <String, dynamic>{
      'search_query': query,
      'search_category': category,
    });
  }

  /// تسجيل التصدير
  static Future<void> logExport(String exportType, String format) async {
    await logEvent('export', parameters: <String, dynamic>{
      'export_type': exportType,
      'format': format,
    });
  }

  /// تسجيل النسخ الاحتياطي
  static Future<void> logBackup(String backupType, bool success) async {
    await logEvent('backup', parameters: <String, dynamic>{
      'backup_type': backupType,
      'success': success,
    });
  }

  /// تسجيل تغيير الإعدادات
  static Future<void> logSettingsChanged(
      String settingName, dynamic value) async {
    await logEvent('settings_changed', parameters: <String, dynamic>{
      'setting_name': settingName,
      'new_value': value.toString(),
    });
  }

  /// تسجيل خطأ
  static Future<void> logError(String error, {String? context}) async {
    await logEvent('error', parameters: <String, dynamic>{
      'error_message': error,
      'context': context,
    });
  }

  /// تسجيل الأداء
  static Future<void> logPerformance(
      String operation, Duration duration) async {
    await logEvent('performance', parameters: <String, dynamic>{
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
    });
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStatistics() {
    if (!_isEnabled) return <String, dynamic>{};

    final List events = _analyticsData['events'] as List? ?? <dynamic>[];
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime thisWeek = today.subtract(Duration(days: now.weekday - 1));
    final DateTime thisMonth = DateTime(now.year, now.month, 1);

    final List todayEvents = events.where((e) {
      final DateTime eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(today);
    }).toList();

    final List weekEvents = events.where((e) {
      final DateTime eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(thisWeek);
    }).toList();

    final List monthEvents = events.where((e) {
      final DateTime eventDate = DateTime.parse(e['timestamp']);
      return eventDate.isAfter(thisMonth);
    }).toList();

    return <String, dynamic>{
      'total_events': events.length,
      'today_events': todayEvents.length,
      'week_events': weekEvents.length,
      'month_events': monthEvents.length,
      'most_used_features': _getMostUsedFeatures(events),
      'screen_views': _getScreenViews(events),
      'error_count': _getErrorCount(events),
    };
  }

  /// الحصول على إحصائيات المبيعات
  static Map<String, dynamic> getSalesAnalytics(List<Sale> sales) {
    if (sales.isEmpty) return <String, dynamic>{};

    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime thisWeek = today.subtract(Duration(days: now.weekday - 1));
    final DateTime thisMonth = DateTime(now.year, now.month, 1);

    final List<Sale> todaySales = sales
        .where((Sale s) => s.date != null && s.date!.isAfter(today))
        .toList();
    final List<Sale> weekSales = sales
        .where((Sale s) => s.date != null && s.date!.isAfter(thisWeek))
        .toList();
    final List<Sale> monthSales = sales
        .where((Sale s) => s.date != null && s.date!.isAfter(thisMonth))
        .toList();

    final double totalRevenue = sales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));
    final double todayRevenue = todaySales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));
    final double weekRevenue = weekSales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));
    final double monthRevenue = monthSales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));

    return <String, dynamic>{
      'total_sales': sales.length,
      'today_sales': todaySales.length,
      'week_sales': weekSales.length,
      'month_sales': monthSales.length,
      'total_revenue': totalRevenue,
      'today_revenue': todayRevenue,
      'week_revenue': weekRevenue,
      'month_revenue': monthRevenue,
      'average_sale': sales.isNotEmpty ? totalRevenue / sales.length : 0,
      'payment_methods': _getPaymentMethodsAnalytics(sales),
      'sales_by_day': _getSalesByDay(sales),
    };
  }

  /// الحصول على إحصائيات المنتجات
  static Map<String, dynamic> getProductAnalytics(List<Product> products) {
    if (products.isEmpty) return <String, dynamic>{};

    final int totalProducts = products.length;
    final int lowStockProducts = products
        .where((Product p) => (p.quantity ?? 0) <= (p.minLevel ?? 10))
        .length;
    final int outOfStockProducts =
        products.where((Product p) => (p.quantity ?? 0) <= 0).length;

    final double totalValue = products.fold<double>(
        0,
        (double sum, Product product) =>
            sum + ((product.quantity ?? 0) * (product.purchasePrice ?? 0)));

    final Map<String, int> categories = <String, int>{};
    for (final Product product in products) {
      final String category = product.category ?? 'غير محدد';
      categories[category] = (categories[category] ?? 0) + 1;
    }

    return <String, dynamic>{
      'total_products': totalProducts,
      'low_stock_products': lowStockProducts,
      'out_of_stock_products': outOfStockProducts,
      'total_inventory_value': totalValue,
      'categories_distribution': categories,
      'average_price': products.isNotEmpty
          ? products.fold<double>(
                  0, (double sum, Product p) => sum + (p.salePrice ?? 0)) /
              products.length
          : 0,
    };
  }

  /// الحصول على إحصائيات العملاء
  static Map<String, dynamic> getCustomerAnalytics(List<Customer> customers) {
    if (customers.isEmpty) return <String, dynamic>{};

    final int totalCustomers = customers.length;
    final int customersWithDebt =
        customers.where((Customer c) => (c.balance ?? 0) < 0).length;
    final int customersWithCredit =
        customers.where((Customer c) => (c.balance ?? 0) > 0).length;

    final double totalDebt = customers.fold<double>(
        0,
        (double sum, Customer customer) =>
            sum +
            (customer.balance ?? 0 < 0 ? (customer.balance ?? 0).abs() : 0));
    final double totalCredit = customers.fold<double>(
        0,
        (double sum, Customer customer) =>
            sum + (customer.balance ?? 0 > 0 ? customer.balance ?? 0 : 0));

    return <String, dynamic>{
      'total_customers': totalCustomers,
      'customers_with_debt': customersWithDebt,
      'customers_with_credit': customersWithCredit,
      'total_debt': totalDebt,
      'total_credit': totalCredit,
      'net_balance': totalCredit - totalDebt,
    };
  }

  /// تصدير البيانات التحليلية
  static Future<String?> exportAnalytics() async {
    if (!_isEnabled) return null;

    try {
      final Map<String, Object> data = <String, Object>{
        'export_date': DateTime.now().toIso8601String(),
        'app_name': AppConstants.appName,
        'version': AppConstants.appVersion,
        'analytics_data': _analyticsData,
        'usage_statistics': getUsageStatistics(),
      };

      final String jsonString =
          const JsonEncoder.withIndent('  ').convert(data);
      // TODO: حفظ الملف
      return jsonString;
    } catch (e) {
      return null;
    }
  }

  /// مسح البيانات التحليلية
  static Future<void> clearAnalytics() async {
    _analyticsData.clear();
    await _saveAnalyticsData();
  }

  /// تفعيل/إلغاء التحليلات
  static Future<void> setAnalyticsEnabled(bool enabled) async {
    _isEnabled = enabled;
    await SettingsHelper.setAnalytics(enabled);

    if (!enabled) {
      await clearAnalytics();
    }
  }

  // دوال مساعدة خاصة
  static void _addEventToData(Map<String, dynamic> event) {
    if (!_analyticsData.containsKey('events')) {
      _analyticsData['events'] = <Map<String, dynamic>>[];
    }

    final List<Map<String, dynamic>> events =
        _analyticsData['events'] as List<Map<String, dynamic>>;
    events.add(event);

    // الاحتفاظ بآخر 1000 حدث فقط
    if (events.length > 1000) {
      events.removeRange(0, events.length - 1000);
    }
  }

  static Future<void> _loadAnalyticsData() async {
    try {
      final Map<String, dynamic>? data = SettingsHelper.getAnalyticsData();
      if (data != null) {
        _analyticsData.addAll(data);
      }
    } catch (e) {
      // تجاهل أخطاء التحميل
    }
  }

  static Future<void> _saveAnalyticsData() async {
    try {
      await SettingsHelper.setAnalyticsData(_analyticsData);
    } catch (e) {
      // تجاهل أخطاء الحفظ
    }
  }

  static Map<String, int> _getMostUsedFeatures(List events) {
    final Map<String, int> features = <String, int>{};

    for (final event in events) {
      final String name = event['name'] as String;
      features[name] = (features[name] ?? 0) + 1;
    }

    final Map<String, int> sortedFeatures = Map.fromEntries(
        features.entries.toList()
          ..sort((MapEntry<String, int> a, MapEntry<String, int> b) =>
              b.value.compareTo(a.value)));

    return Map.fromEntries(sortedFeatures.entries.take(10));
  }

  static Map<String, int> _getScreenViews(List events) {
    final Map<String, int> screens = <String, int>{};

    for (final event in events) {
      if (event['name'] == 'screen_view') {
        final String screenName = event['parameters']['screen_name'] as String;
        screens[screenName] = (screens[screenName] ?? 0) + 1;
      }
    }

    return screens;
  }

  static int _getErrorCount(List events) {
    return events.where((e) => e['name'] == 'error').length;
  }

  static Map<String, int> _getPaymentMethodsAnalytics(List<Sale> sales) {
    final Map<String, int> methods = <String, int>{};

    for (final Sale sale in sales) {
      final method = sale.paymentMethod ?? 'غير محدد';
      methods[method] = (methods[method] ?? 0) + 1;
    }

    return methods;
  }

  static Map<String, double> _getSalesByDay(List<Sale> sales) {
    final Map<String, double> salesByDay = <String, double>{};

    for (final Sale sale in sales) {
      if (sale.date != null) {
        final String day = DateHelper.formatDate(sale.date!);
        salesByDay[day] = (salesByDay[day] ?? 0) + (sale.total ?? 0);
      }
    }

    return salesByDay;
  }
}
