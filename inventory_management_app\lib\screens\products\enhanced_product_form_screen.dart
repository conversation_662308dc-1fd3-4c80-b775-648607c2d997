import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_constants.dart';
import '../../widgets/custom_buttons.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../utils/validators.dart';
import '../../utils/snackbar_helper.dart';
import '../../providers/product_provider.dart';
import '../../models/product.dart';

/// شاشة إضافة/تعديل المنتج المحسنة
class EnhancedProductFormScreen extends StatefulWidget {
  final String? productId;
  final bool isEditing;

  const EnhancedProductFormScreen({
    super.key,
    this.productId,
    this.isEditing = false,
  });

  @override
  State<EnhancedProductFormScreen> createState() =>
      _EnhancedProductFormScreenState();
}

class _EnhancedProductFormScreenState extends State<EnhancedProductFormScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _purchasePriceController =
      TextEditingController();
  final TextEditingController _salePriceController = TextEditingController();
  final TextEditingController _minLevelController = TextEditingController();
  final TextEditingController _barcodeController = TextEditingController();

  String? _selectedCategory;
  String? _selectedUnit;
  bool _isLoading = false;
  Product? _existingProduct;

  final List<String> _categories = <String>[
    'مواد غذائية',
    'مشروبات',
    'منظفات',
    'أدوات منزلية',
    'مستحضرات تجميل',
    'أدوية',
    'ملابس',
    'إلكترونيات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.isEditing && widget.productId != null) {
      _loadProduct();
    }
    _initializeDefaults();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _purchasePriceController.dispose();
    _salePriceController.dispose();
    _minLevelController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }

  void _initializeDefaults() {
    if (!widget.isEditing) {
      _quantityController.text = '0';
      _minLevelController.text = AppConstants.defaultMinStockLevel.toString();
      _selectedUnit = AppConstants.defaultUnits.first;
    }
  }

  void _loadProduct() async {
    setState(() => _isLoading = true);

    try {
      final ProductProvider productProvider = context.read<ProductProvider>();
      final Product? product = productProvider.products
          .where((Product p) => p.id.toString() == widget.productId)
          .firstOrNull;

      if (product != null) {
        _existingProduct = product;
        _populateForm(product);
      }
    } catch (e) {
      SnackBarHelper.showError(context, 'فشل في تحميل بيانات المنتج');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _populateForm(Product product) {
    _nameController.text = product.name ?? '';
    _descriptionController.text = product.description ?? '';
    _quantityController.text = (product.quantity ?? 0).toString();
    _purchasePriceController.text = (product.purchasePrice ?? 0).toString();
    _salePriceController.text = (product.salePrice ?? 0).toString();
    _minLevelController.text = (product.minLevel ?? 0).toString();
    _barcodeController.text = product.barcode ?? '';
    _selectedCategory = product.category;
    _selectedUnit = product.unit ?? AppConstants.defaultUnits.first;
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildForm(),
        bottomNavigationBar: _buildBottomActions(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'),
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      actions: <Widget>[
        if (widget.isEditing)
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteProduct,
          ),
      ],
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildSectionTitle('المعلومات الأساسية'),
            _buildBasicInfoSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('معلومات المخزون'),
            _buildInventorySection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('معلومات الأسعار'),
            _buildPricingSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle('معلومات إضافية'),
            _buildAdditionalInfoSection(),
            const SizedBox(height: AppDimensions.paddingXL),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Text(
        title,
        style: AppStyles.titleMedium.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج *',
                prefixIcon: Icon(Icons.inventory_2),
              ),
              validator: Validators.validateName,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج',
                prefixIcon: Icon(Icons.description),
              ),
              validator: Validators.validateDescription,
              maxLines: 3,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'الفئة *',
                prefixIcon: Icon(Icons.category),
              ),
              items: _categories.map((String category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
              validator: (String? value) => Validators.validateDropdown(
                value,
                fieldName: 'الفئة',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            Row(
              children: <Widget>[
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية الحالية *',
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                    validator: Validators.validateQuantity,
                    textInputAction: TextInputAction.next,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedUnit,
                    decoration: const InputDecoration(
                      labelText: 'الوحدة *',
                    ),
                    items: AppConstants.defaultUnits.map((String unit) {
                      return DropdownMenuItem(
                        value: unit,
                        child: Text(unit),
                      );
                    }).toList(),
                    onChanged: (String? value) {
                      setState(() {
                        _selectedUnit = value;
                      });
                    },
                    validator: (String? value) => Validators.validateDropdown(
                      value,
                      fieldName: 'الوحدة',
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _minLevelController,
              decoration: const InputDecoration(
                labelText: 'الحد الأدنى للمخزون *',
                prefixIcon: Icon(Icons.warning),
                helperText: 'سيتم تنبيهك عند الوصول لهذا الحد',
              ),
              keyboardType: TextInputType.number,
              validator: (String? value) => Validators.validateNumber(
                value,
                min: 0,
                fieldName: 'الحد الأدنى',
              ),
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _purchasePriceController,
              decoration: const InputDecoration(
                labelText: 'سعر الشراء *',
                prefixIcon: Icon(Icons.shopping_cart),
                suffixText: 'ر.س',
              ),
              keyboardType: TextInputType.number,
              validator: Validators.validatePrice,
              textInputAction: TextInputAction.next,
              onChanged: _calculateSuggestedSalePrice,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _salePriceController,
              decoration: const InputDecoration(
                labelText: 'سعر البيع *',
                prefixIcon: Icon(Icons.sell),
                suffixText: 'ر.س',
              ),
              keyboardType: TextInputType.number,
              validator: Validators.validatePrice,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildProfitMarginInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitMarginInfo() {
    final double purchasePrice =
        double.tryParse(_purchasePriceController.text) ?? 0;
    final double salePrice = double.tryParse(_salePriceController.text) ?? 0;
    final double profit = salePrice - purchasePrice;
    final num margin = purchasePrice > 0 ? (profit / purchasePrice) * 100 : 0;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: AppColors.infoLight,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            'الربح: ${profit.toStringAsFixed(2)} ر.س',
            style: AppStyles.bodySmall.copyWith(
              color: profit >= 0 ? AppColors.success : AppColors.error,
            ),
          ),
          Text(
            'هامش الربح: ${margin.toStringAsFixed(1)}%',
            style: AppStyles.bodySmall.copyWith(
              color: margin >= 0 ? AppColors.success : AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: <Widget>[
            TextFormField(
              controller: _barcodeController,
              decoration: InputDecoration(
                labelText: 'الباركود',
                prefixIcon: const Icon(Icons.qr_code),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  onPressed: _scanBarcode,
                ),
              ),
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: <Widget>[
            Expanded(
              child: CustomSecondaryButton(
                text: 'إلغاء',
                onPressed: _handleCancel,
                isFullWidth: true,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              flex: 2,
              child: CustomPrimaryButton(
                text: widget.isEditing ? 'حفظ التغييرات' : 'إضافة المنتج',
                onPressed: _saveProduct,
                icon: widget.isEditing ? Icons.save : Icons.add,
                isLoading: _isLoading,
                isFullWidth: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _calculateSuggestedSalePrice(String value) {
    final double? purchasePrice = double.tryParse(value);
    if (purchasePrice != null && purchasePrice > 0) {
      // اقتراح سعر بيع بهامش ربح 30%
      final double suggestedPrice = purchasePrice * 1.3;
      if (_salePriceController.text.isEmpty) {
        _salePriceController.text = suggestedPrice.toStringAsFixed(2);
      }
    }
  }

  void _scanBarcode() {
    // TODO: تنفيذ مسح الباركود
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ مسح الباركود قريباً');
  }

  void _handleCancel() async {
    if (_hasUnsavedChanges()) {
      final bool? confirmed = await EnhancedConfirmationDialog.show(
        context,
        title: 'تجاهل التغييرات؟',
        message: 'هل تريد تجاهل التغييرات غير المحفوظة؟',
        icon: Icons.warning,
      );

      if (confirmed == true) {
        context.pop();
      }
    } else {
      context.pop();
    }
  }

  bool _hasUnsavedChanges() {
    if (!widget.isEditing) {
      return _nameController.text.isNotEmpty ||
          _descriptionController.text.isNotEmpty ||
          _quantityController.text != '0' ||
          _purchasePriceController.text.isNotEmpty ||
          _salePriceController.text.isNotEmpty;
    }

    if (_existingProduct == null) return false;

    return _nameController.text != (_existingProduct!.name ?? '') ||
        _descriptionController.text != (_existingProduct!.description ?? '') ||
        _quantityController.text !=
            (_existingProduct!.quantity ?? 0).toString() ||
        _purchasePriceController.text !=
            (_existingProduct!.purchasePrice ?? 0).toString() ||
        _salePriceController.text !=
            (_existingProduct!.salePrice ?? 0).toString() ||
        _selectedCategory != _existingProduct!.category ||
        _selectedUnit != _existingProduct!.unit;
  }

  void _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      SnackBarHelper.showError(context, 'يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final Product product = Product(
        id: widget.isEditing ? _existingProduct?.id : null,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        category: _selectedCategory,
        unit: _selectedUnit,
        quantity: int.tryParse(_quantityController.text) ?? 0,
        purchasePrice: double.tryParse(_purchasePriceController.text) ?? 0,
        salePrice: double.tryParse(_salePriceController.text) ?? 0,
        minLevel: int.tryParse(_minLevelController.text) ?? 0,
        barcode: _barcodeController.text.trim().isEmpty
            ? null
            : _barcodeController.text.trim(),
        createdAt:
            widget.isEditing ? _existingProduct?.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final ProductProvider productProvider = context.read<ProductProvider>();

      if (widget.isEditing) {
        await productProvider.updateProduct(product);
        SnackBarHelper.showSuccess(context, 'تم تحديث المنتج بنجاح');
      } else {
        await productProvider.addProduct(product);
        SnackBarHelper.showSuccess(context, 'تم إضافة المنتج بنجاح');
      }

      context.pop();
    } catch (e) {
      SnackBarHelper.showError(
        context,
        widget.isEditing
            ? 'فشل في تحديث المنتج: $e'
            : 'فشل في إضافة المنتج: $e',
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _deleteProduct() async {
    if (_existingProduct == null) return;

    final bool? confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: _existingProduct!.name ?? 'المنتج',
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      try {
        await context
            .read<ProductProvider>()
            .deleteProduct(_existingProduct!.id!);
        SnackBarHelper.showSuccess(context, 'تم حذف المنتج بنجاح');
        context.pop();
      } catch (e) {
        SnackBarHelper.showError(context, 'فشل في حذف المنتج: $e');
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }
}
