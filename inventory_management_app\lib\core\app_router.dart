import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:inventory_management_app/screens/splash/splash_screen.dart';
import 'package:inventory_management_app/screens/onboarding/onboarding_screen.dart';
// import 'package:inventory_management_app/screens/dashboard/modern_dashboard_screen.dart';
import 'package:inventory_management_app/screens/dashboard/enhanced_dashboard_screen.dart';
import 'package:inventory_management_app/screens/products/enhanced_product_list_screen.dart';
import 'package:inventory_management_app/screens/products/enhanced_product_form_screen.dart';
import 'package:inventory_management_app/screens/products/enhanced_product_details_screen.dart';
import 'package:inventory_management_app/screens/customers/enhanced_customer_list_screen.dart';
import 'package:inventory_management_app/screens/customers/enhanced_customer_form_screen.dart';
import 'package:inventory_management_app/screens/sales/enhanced_sale_list_screen.dart';
import 'package:inventory_management_app/screens/reports/enhanced_reports_screen.dart';
import 'package:inventory_management_app/screens/settings/enhanced_settings_screen.dart';
import 'package:inventory_management_app/screens/customers/customers_screen.dart';
import 'package:inventory_management_app/screens/suppliers/suppliers_screen.dart';
import 'package:inventory_management_app/screens/orders/orders_screen.dart';
import 'package:inventory_management_app/screens/sales/sales_screen.dart';
import 'package:inventory_management_app/screens/purchases/purchases_screen.dart';
import 'package:inventory_management_app/screens/expenses/expenses_screen.dart';
import 'package:inventory_management_app/screens/categories/categories_screen.dart';
import 'package:inventory_management_app/screens/units/units_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedDashboardScreen(),
      ),
      GoRoute(
        path: '/products',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedProductListScreen(),
      ),
      GoRoute(
        path: '/products/add',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedProductFormScreen(),
      ),
      GoRoute(
        path: '/products/edit/:id',
        builder: (BuildContext context, GoRouterState state) =>
            EnhancedProductFormScreen(
          productId: state.pathParameters['id'],
          isEditing: true,
        ),
      ),
      GoRoute(
        path: '/products/details/:id',
        builder: (BuildContext context, GoRouterState state) =>
            EnhancedProductDetailsScreen(
          productId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedCustomerListScreen(),
      ),
      GoRoute(
        path: '/customers/add',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedCustomerFormScreen(),
      ),
      GoRoute(
        path: '/customers/edit/:id',
        builder: (BuildContext context, GoRouterState state) =>
            EnhancedCustomerFormScreen(
          customerId: state.pathParameters['id'],
          isEditing: true,
        ),
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) => OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedSaleListScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            const ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) => UnitsScreen(),
      ),
      GoRoute(
      // GoRoute(
      //   path: '/backup',
      //   builder: (BuildContext context, GoRouterState state) => BackupScreen(),
      // ),
        path: '/reports',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedSettingsScreen(),
      ),
    ],
  );
}
