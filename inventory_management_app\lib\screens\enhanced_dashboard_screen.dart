import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:provider/provider.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';
import '../config/app_colors.dart';
import '../config/app_styles.dart';
import '../widgets/custom_elevated_button.dart';
// import '../widgets/loading_indicator_widget.dart'; // غير مستخدم
// الملفات المحذوفة - تم إزالة الاستيرادات
// import 'enhanced_reports_screen.dart';
// import 'advanced_settings_screen.dart';
// import 'advanced_analytics_screen.dart';
// import 'mobile_features_screen.dart';
// import 'advanced_search_screen.dart';
// import 'gamification_screen.dart';
// import 'user_management_screen.dart';
// import 'support_screen.dart';
// استيراد الشاشات الجديدة
import 'invoices/create_sale_invoice_screen.dart';
import 'invoices/create_purchase_invoice_screen.dart';
import 'orders/create_shop_order_screen.dart';
import 'activity/activity_list_screen.dart';
import 'invoices/sale_invoice_list_screen.dart';
import 'invoices/purchase_invoice_list_screen.dart';
// import 'orders/order_list_screen.dart'; // غير مستخدم
// import 'expenses/expense_list_screen.dart'; // غير مستخدم
import 'statements/customer_statement_screen.dart';
import 'inventory/internal_transfer_screen.dart';
import 'statements/supplier_statement_screen.dart';
import 'analytics/analytics_screen.dart';
import 'settings/enhanced_settings_screen.dart';
// import 'backup/backup_dialog.dart'; // ملف محذوف
import 'inventory/store_inventory_adjustment_screen.dart';

class EnhancedDashboardScreen extends StatelessWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(
            'أسامة ماركت',
            style: AppStyles.titleLarge.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          centerTitle: true,
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {
                // TODO: إضافة الإشعارات
              },
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                // TODO: إضافة البحث
              },
            ),
          ],
        ),
        drawer: _buildDrawer(context),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // ترحيب
              _buildWelcomeCard(),

              const SizedBox(height: 24),

              // أزرار الوصول السريع الجديدة
              _buildQuickAccessButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final DateTime now = DateTime.now();
    final String timeOfDay = now.hour < 12
        ? 'صباح الخير'
        : now.hour < 17
            ? 'مساء الخير'
            : 'مساء الخير';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: <Color>[AppColors.primary, AppColors.primaryLight],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: AppColors.elevatedShadow,
      ),
      child: Row(
        children: <Widget>[
          // أيقونة المتجر
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: const Icon(
              Icons.store_outlined,
              size: 32,
              color: AppColors.textOnPrimary,
            ),
          ),

          const SizedBox(width: AppDimensions.paddingM),

          // النصوص
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  timeOfDay,
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'أسامة ماركت',
                  style: AppStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إدارة ذكية لمتجرك',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),

          // التاريخ
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Text(
                '${now.day}',
                style: AppStyles.headlineMedium.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _getMonthName(now.month),
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textOnPrimary.withOpacity(0.8),
                ),
              ),
              Text(
                '${now.year}',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textOnPrimary.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const List<String> months = <String>[
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return months[month - 1];
  }

  /// أزرار الوصول السريع الجديدة
  Widget _buildQuickAccessButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'الإجراءات السريعة',
          style: AppStyles.titleLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingM),

        // شبكة الأزرار
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.1,
          crossAxisSpacing: AppDimensions.paddingM,
          mainAxisSpacing: AppDimensions.paddingM,
          children: <Widget>[
            QuickActionButton(
              title: 'فاتورة بيع',
              subtitle: 'بيع جديد',
              icon: Icons.point_of_sale,
              color: AppColors.sales,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreateSaleInvoiceScreen(),
                  ),
                );
              },
            ),
            QuickActionButton(
              title: 'فاتورة توريد',
              subtitle: 'شراء جديد',
              icon: Icons.local_shipping,
              color: AppColors.purchases,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreatePurchaseInvoiceScreen(),
                  ),
                );
              },
            ),
            QuickActionButton(
              title: 'طلبية محل',
              subtitle: 'طلب جديد',
              icon: Icons.shopping_cart,
              color: AppColors.inventory,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreateShopOrderScreen(),
                  ),
                );
              },
            ),
            QuickActionButton(
              title: 'قائمة الأنشطة',
              subtitle: 'عرض الأنشطة',
              icon: Icons.list_alt,
              color: AppColors.reports,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const ActivityListScreen(),
                  ),
                );
              },
            ),
            QuickActionButton(
              title: 'فواتير البيع',
              subtitle: 'عرض الفواتير',
              icon: Icons.receipt_long,
              color: AppColors.customers,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const SaleInvoiceListScreen(),
                  ),
                );
              },
            ),
            QuickActionButton(
              title: 'فواتير التوريد',
              subtitle: 'عرض المشتريات',
              icon: Icons.assignment_turned_in,
              color: AppColors.suppliers,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const PurchaseInvoiceListScreen(),
                  ),
                );
              },
            ),
          ],
        ),

        const SizedBox(height: AppDimensions.paddingL),

        // إحصائيات سريعة
        _buildQuickStats(context),
      ],
    );
  }

  /// إحصائيات سريعة
  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'نظرة سريعة',
          style: AppStyles.titleLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingM),

        // بطاقات الإحصائيات
        Consumer3<ProductProvider, SaleProvider, CustomerProvider>(
          builder: (BuildContext context,
              ProductProvider productProvider,
              SaleProvider saleProvider,
              CustomerProvider customerProvider,
              Widget? child) {
            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: AppDimensions.paddingM,
              mainAxisSpacing: AppDimensions.paddingM,
              children: <Widget>[
                _buildStatCard(
                  title: 'إجمالي المنتجات',
                  value: '${productProvider.products.length}',
                  icon: Icons.inventory_2_outlined,
                  color: AppColors.inventory,
                ),
                _buildStatCard(
                  title: 'إجمالي العملاء',
                  value: '${customerProvider.customers.length}',
                  icon: Icons.people_outline,
                  color: AppColors.customers,
                ),
                _buildStatCard(
                  title: 'مبيعات اليوم',
                  value: '${_getTodaySales(saleProvider)} ر.س',
                  icon: Icons.trending_up,
                  color: AppColors.sales,
                ),
                _buildStatCard(
                  title: 'المخزون المنخفض',
                  value: '${_getLowStockCount(productProvider)}',
                  icon: Icons.warning_outlined,
                  color: AppColors.warning,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: AppColors.cardShadow,
        border: Border(
          top: BorderSide(color: color, width: 3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Icon(
                icon,
                color: color,
                size: AppDimensions.iconL,
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  Icons.arrow_upward,
                  color: color,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            value,
            style: AppStyles.headlineSmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  double _getTodaySales(SaleProvider saleProvider) {
    final DateTime today = DateTime.now();
    final Iterable<Sale> todaySales = saleProvider.sales.where((Sale sale) {
      if (sale.date == null) return false;
      try {
        final DateTime saleDate = DateTime.parse(sale.date!);
        return saleDate.year == today.year &&
            saleDate.month == today.month &&
            saleDate.day == today.day;
      } catch (e) {
        return false;
      }
    });

    return todaySales.fold(
        0.0, (double sum, Sale sale) => sum + (sale.total ?? 0.0));
  }

  int _getLowStockCount(ProductProvider productProvider) {
    return productProvider.products.where((Product product) {
      return (product.warehouseQuantity ?? 0) < 10;
    }).length;
  }

  /// بناء زر الوصول السريع
  Widget _buildQuickAccessButton({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        elevation: 2,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(icon, size: 28),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء الـ Drawer الجانبي
  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: <Widget>[
          // Header
          _buildDrawerHeader(),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: <Widget>[
                // التنقل الرئيسي
                _buildDrawerSection('التنقل الرئيسي', <Widget>[
                  _buildDrawerItem(
                    icon: Icons.home,
                    title: 'الرئيسية',
                    onTap: () => Navigator.pop(context),
                  ),
                  _buildDrawerItem(
                    icon: Icons.inventory,
                    title: 'المنتجات',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to products
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.people,
                    title: 'العملاء',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to customers
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.local_shipping,
                    title: 'الموردون',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Navigate to suppliers
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.swap_horiz,
                    title: 'تحويل المخزون',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const InternalTransferScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.inventory_2,
                    title: 'جرد مخزون البقالة',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const StoreInventoryAdjustmentScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // التقارير والكشوفات
                _buildDrawerSection('التقارير والكشوفات', <Widget>[
                  _buildDrawerItem(
                    icon: Icons.people_alt,
                    title: 'كشف العملاء',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const CustomerStatementScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.business_center,
                    title: 'كشف الموردين',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const SupplierStatementScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.analytics,
                    title: 'الإحصائيات والتقارير',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const AnalyticsScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // الأدوات والإعدادات
                _buildDrawerSection('الأدوات والإعدادات', <Widget>[
                  _buildDrawerItem(
                    icon: Icons.backup,
                    title: 'النسخ الاحتياطي',
                    onTap: () {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('ميزة النسخ الاحتياطي قيد التطوير'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'الإعدادات',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              const EnhancedSettingsScreen(),
                        ),
                      );
                    },
                  ),
                ]),

                const Divider(),

                // تسجيل الخروج
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: 'تسجيل الخروج',
                  textColor: Colors.red,
                  onTap: () => _showLogoutDialog(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: <Color>[Colors.blue[600]!, Colors.blue[400]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
      ),
      child: const SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white,
                child: Icon(
                  Icons.store,
                  size: 35,
                  color: Colors.blue,
                ),
              ),
              SizedBox(height: 12),
              Text(
                'نظام إدارة المخزون',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'الإصدار 1.0',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              letterSpacing: 1.2,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Colors.grey[700],
        size: 22,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          color: textColor ?? Colors.grey[800],
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      hoverColor: Colors.grey[100],
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // TODO: Implement logout functionality
      // For now, just show a message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
