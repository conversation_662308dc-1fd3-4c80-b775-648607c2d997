import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:path/path.dart';
import 'package:inventory_management_app/data/database_helper.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static DatabaseService get instance => _instance;
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDatabase();
    return _database!;
  }

  Future<Database> _initializeDatabase() async {
    // Initialize database factory for web
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    final String databasePath = await getDatabasesPath();
    final String path = join(databasePath, 'inventory_management.db');

    return openDatabase(
      path,
      version: 6, // إصلاح مشكلة الفهارس المكررة
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      print('🔄 Creating database tables (version $version)...');

      // إنشاء الجداول الأساسية
      await db.execute(createProductsTable);
      await db.execute(createCustomersTable);
      await db.execute(createSuppliersTable);
      await db.execute(createCategoriesTable);
      await db.execute(createUnitsTable);
      print('✅ Created basic tables');

      // إنشاء جداول المعاملات
      await db.execute(createOrdersTable);
      await db.execute(createOrderItemsTable);
      await db.execute(createSalesTable);
      await db.execute(createSaleItemsTable);
      await db.execute(createPurchasesTable);
      await db.execute(createPurchaseItemsTable);
      print('✅ Created transaction tables');

      // إنشاء الجداول المساعدة
      await db.execute(createExpensesTable);
      await db.execute(createBackupsTable);
      await db.execute(createActivitiesTable);
      await db.execute(createTransactionsTable);
      await db.execute(createDailySummaryTable);
      await db.execute(createCustomerStatementTable);
      await db.execute(createSupplierStatementTable);
      print('✅ Created auxiliary tables');

      // إنشاء الجداول المتقدمة
      await db.execute(createInternalTransfersTable);
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created advanced tables');

      // إنشاء الفهارس
      await _createIndexes(db);

      print('🎉 Database created successfully with version $version');
    } catch (e) {
      print('❌ Error creating database: $e');
      rethrow;
    }
  }

  /// Handle database upgrades safely without losing data
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('🔄 Upgrading database from version $oldVersion to $newVersion');

    if (oldVersion < 2) {
      // Upgrade from version 1 to 2: Fix expenses table structure
      await _upgradeToVersion2(db);
    }

    if (oldVersion < 3) {
      // Upgrade from version 2 to 3: Add warehouse and store system support
      await _upgradeToVersion3(db);
    }

    if (oldVersion < 4) {
      // Upgrade from version 3 to 4: Add internal transfers support
      await _upgradeToVersion4(db);
    }

    if (oldVersion < 5) {
      // Upgrade from version 4 to 5: Add store inventory adjustments support
      await _upgradeToVersion5(db);
    }

    if (oldVersion < 6) {
      // Upgrade from version 5 to 6: Fix duplicate indexes issue
      await _upgradeToVersion6(db);
    }

    // Future upgrades can be added here
    // if (oldVersion < 7) {
    //   await _upgradeToVersion7(db);
    // }
  }

  /// Upgrade to version 2: Fix expenses table structure
  Future<void> _upgradeToVersion2(Database db) async {
    try {
      print('📊 Upgrading expenses table structure...');

      // Step 1: Check if old expenses table exists and has data
      final List<Map<String, dynamic>> existingExpenses = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='expenses'");

      List<Map<String, dynamic>> oldExpensesData = <Map<String, dynamic>>[];
      if (existingExpenses.isNotEmpty) {
        // Backup existing data
        try {
          oldExpensesData = await db.query('expenses');
          print('💾 Backed up ${oldExpensesData.length} existing expenses');
        } catch (e) {
          print('⚠️ No existing expenses data to backup: $e');
        }
      }

      // Step 2: Drop old expenses table if exists
      await db.execute('DROP TABLE IF EXISTS expenses');
      print('🗑️ Dropped old expenses table');

      // Step 3: Create new expenses table with correct structure
      await db.execute(createExpensesTable);
      print('✅ Created new expenses table');

      // Step 4: Migrate old data if any exists
      if (oldExpensesData.isNotEmpty) {
        await _migrateExpensesData(db, oldExpensesData);
      }

      print('🎉 Successfully upgraded expenses table to version 2');
    } catch (e) {
      print('❌ Error upgrading to version 2: $e');
      // If upgrade fails, recreate the table with new structure
      await db.execute('DROP TABLE IF EXISTS expenses');
      await db.execute(createExpensesTable);
      print('🔧 Recreated expenses table after upgrade error');
    }
  }

  /// Upgrade to version 3: Add warehouse and store system support
  Future<void> _upgradeToVersion3(Database db) async {
    try {
      print('🏪 Upgrading to warehouse and store system (version 3)...');

      // Add new columns to products table
      await db.execute(
          'ALTER TABLE products ADD COLUMN retailPrice REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE products ADD COLUMN warehouseQuantity INTEGER DEFAULT 0');
      await db.execute(
          'ALTER TABLE products ADD COLUMN storeQuantity INTEGER DEFAULT 0');
      print('✅ Added new columns to products table');

      // Add new columns to sales table
      await db.execute(
          'ALTER TABLE sales ADD COLUMN totalWholesaleAmount REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE sales ADD COLUMN totalRetailAmount REAL DEFAULT 0');
      await db.execute(
          'ALTER TABLE sales ADD COLUMN remainingRetailAmount REAL DEFAULT 0');
      await db.execute('ALTER TABLE sales ADD COLUMN notesForRetailItems TEXT');
      print('✅ Added new columns to sales table');

      // Add new column to sale_items table
      await db.execute(
          'ALTER TABLE sale_items ADD COLUMN itemType TEXT DEFAULT \'wholesale\'');
      print('✅ Added itemType column to sale_items table');

      // Migrate existing quantity data to warehouse
      await db.execute('''
        UPDATE products
        SET warehouseQuantity = COALESCE(quantity, 0),
            storeQuantity = 0
        WHERE warehouseQuantity IS NULL OR warehouseQuantity = 0
      ''');
      print('📦 Migrated existing quantities to warehouse');

      // Set default retail prices (same as wholesale for now)
      await db.execute('''
        UPDATE products
        SET retailPrice = COALESCE(price, 0)
        WHERE retailPrice IS NULL OR retailPrice = 0
      ''');
      print('💰 Set default retail prices');

      print(
          '🎉 Successfully upgraded to version 3 (warehouse and store system)');
    } catch (e) {
      print('❌ Error upgrading to version 3: $e');
      rethrow;
    }
  }

  /// Upgrade to version 4: Add internal transfers support
  Future<void> _upgradeToVersion4(Database db) async {
    try {
      print('🔄 Upgrading to internal transfers system (version 4)...');

      // Create internal_transfers table
      await db.execute(createInternalTransfersTable);
      print('✅ Created internal_transfers table');

      // Note: Indexes will be created by _createIndexes() if needed

      print(
          '🎉 Successfully upgraded to version 4 (internal transfers system)');
    } catch (e) {
      print('❌ Error upgrading to version 4: $e');
      rethrow;
    }
  }

  /// Upgrade to version 5: Add store inventory adjustments support
  Future<void> _upgradeToVersion5(Database db) async {
    try {
      print(
          '🔄 Upgrading to store inventory adjustments system (version 5)...');

      // Create store_inventory_adjustments table
      await db.execute(createStoreInventoryAdjustmentsTable);
      print('✅ Created store_inventory_adjustments table');

      // Note: Indexes will be created by _createIndexes() if needed

      print(
          '🎉 Successfully upgraded to version 5 (store inventory adjustments system)');
    } catch (e) {
      print('❌ Error upgrading to version 5: $e');
      rethrow;
    }
  }

  /// Upgrade to version 6: Fix duplicate indexes issue
  Future<void> _upgradeToVersion6(Database db) async {
    try {
      print('🔧 Upgrading to fix indexes issue (version 6)...');

      // إعادة إنشاء جميع الفهارس بشكل صحيح
      await _createIndexes(db);

      print('🎉 Successfully upgraded to version 6 (fixed indexes issue)');
    } catch (e) {
      print('❌ Error upgrading to version 6: $e');
      // لا نرمي الخطأ هنا لأن الفهارس ليست حرجة للتشغيل
      print('⚠️ Continuing without some indexes...');
    }
  }

  /// Migrate old expenses data to new table structure
  Future<void> _migrateExpensesData(
      Database db, List<Map<String, dynamic>> oldData) async {
    try {
      for (final Map<String, dynamic> oldExpense in oldData) {
        // Convert old structure to new structure
        final Map<String, dynamic> newExpense = <String, dynamic>{
          'expenseDate': oldExpense['date'], // date -> expenseDate
          'category': _convertCategoryIdToString(
              oldExpense['categoryId']), // categoryId -> category
          'amount': oldExpense['amount'],
          'description':
              oldExpense['notes'] ?? '', // notes -> description (fallback)
          'notes': oldExpense['notes'],
          'status': 'active', // default status
        };

        await db.insert('expenses', newExpense);
      }
      print('📦 Migrated ${oldData.length} expenses to new structure');
    } catch (e) {
      print('⚠️ Error migrating expenses data: $e');
    }
  }

  /// Convert old categoryId to new category string
  String _convertCategoryIdToString(dynamic categoryId) {
    if (categoryId == null) return 'miscellaneous';

    // Map old category IDs to new category strings
    switch (categoryId) {
      case 1:
        return 'rent';
      case 2:
        return 'salaries';
      case 3:
        return 'supplies';
      case 4:
        return 'utilities';
      case 5:
        return 'maintenance';
      case 6:
        return 'marketing';
      case 7:
        return 'transportation';
      default:
        return 'miscellaneous';
    }
  }

  /// Create performance indexes for frequently queried columns
  Future<void> _createIndexes(Database db) async {
    // استخدام الفهارس الموحدة من database_helper.dart
    for (final String index in createIndexes) {
      try {
        await db.execute(index);
      } catch (e) {
        print('⚠️ Warning creating index: $e');
        // Continue with other indexes even if one fails
      }
    }
    print('✅ All database indexes created successfully');
  }

  /// Delete database (for testing purposes only)
  /// ⚠️ WARNING: This will delete all data permanently!
  Future<void> deleteDatabaseForTesting() async {
    try {
      final String databasePath = await getDatabasesPath();
      final String path = join(databasePath, 'inventory_management.db');

      // Close current database connection
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // Delete the database file
      await deleteDatabase(path);
      print('🗑️ Database deleted successfully for testing');
    } catch (e) {
      print('❌ Error deleting database: $e');
    }
  }

  /// Reset database connection (force recreation)
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Get database file path
  Future<String> getDatabaseFilePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }

  /// Restore database from a backup file
  Future<void> restoreDatabaseFromFile(String backupFilePath) async {
    try {
      final String currentDbPath = await getDatabaseFilePath();

      // Close current database connection
      await resetDatabase();

      // Copy backup file to current database location
      final File backupFile = File(backupFilePath);

      if (!await backupFile.exists()) {
        throw Exception('Backup file does not exist: $backupFilePath');
      }

      await backupFile.copy(currentDbPath);

      print('✅ Database restored successfully from $backupFilePath');
    } catch (e) {
      print('❌ Error restoring database from file: $e');
      rethrow;
    }
  }

  /// Get database file path
  Future<String> getDatabasePath() async {
    final String databasePath = await getDatabasesPath();
    return join(databasePath, 'inventory_management.db');
  }
}
